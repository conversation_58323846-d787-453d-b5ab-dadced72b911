import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/screens/screen.dart';
import 'package:flutter/cupertino.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    final args = settings.arguments;

    CupertinoPageRoute buildRoute(Widget widget) =>
        CupertinoPageRoute(builder: (_) => widget);

    switch (settings.name) {
      case RoutePath.splashScreen:
        return buildRoute(const SplashScreen());
      case RoutePath.onboardingScreen:
        return buildRoute(const OnboardingScreen());
      case RoutePath.bottomNavScreen:
        final arg = args as DashArg?;
        return buildRoute(BottomNavScreen(args: arg));

      //  Auth
      case RoutePath.welcomeScreen:
        return buildRoute(const WelcomeBackScreen());
      case RoutePath.registerScreen:
        return buildRoute(const RegisterScreen());
      case RoutePath.loginScreen:
        return buildRoute(const LoginScreen());
      case RoutePath.otpScreen:
        return buildRoute(const OtpScreen());

      // Shop
      case RoutePath.shopScreen:
        return buildRoute(const ShopScreen());
      case RoutePath.shopProductScreen:
        if (args is String) {
          return buildRoute(ShopProductScreen(category: args));
        }
        return errorScreen('No route defined for ${settings.name}');

      case RoutePath.productDetailsScreen:
        if (args is Variation) {
          return buildRoute(ProductDetailsScreen(product: args));
        }
        return errorScreen('No route defined for ${settings.name}');

      case RoutePath.searchProductScreen:
        return buildRoute(const SearchProductScreen());

      // Cart
      case RoutePath.cartScreen:
        return buildRoute(const CartScreen());
      case RoutePath.checkoutScreen:
        if (args is CartModel) {
          return buildRoute(CheckoutScreen(cartModel: args));
        }
        return errorScreen('No route defined for ${settings.name}');

      // Orders
      case RoutePath.orderScreen:
        return buildRoute(const OrderScreen());
      case RoutePath.orderDetailsScreen:
        if (args is OrderModel) {
          return buildRoute(OrderDetailsScreen(order: args));
        }
        return errorScreen('No route defined for ${settings.name}');
      case RoutePath.orderSuccessScreen:
        return buildRoute(const OrderSuccessScreen());

      case RoutePath.addressScreen:
        return buildRoute(const AddressScreen());
      case RoutePath.addAddressScreen:
        return buildRoute(const AddAddressScreen());

      // Profile
      case RoutePath.profileScreen:
        return buildRoute(const ProfileScreen());
      case RoutePath.editProfileScreen:
        return buildRoute(const EditProfileScreen());
      case RoutePath.deleteAccountScreen:
        return buildRoute(const DeleteAccountScreen());
      case RoutePath.wishlistScreen:
        return buildRoute(const WishlistScreen());

      case RoutePath.referralScreen:
        return buildRoute(const ReferralScreen());
      case RoutePath.notificationScreen:
        return buildRoute(const NotificationScreen());
      case RoutePath.walletScreen:
        return buildRoute(const WalletScreen());

      case RoutePath.customWebViewScreen:
        if (args is WebViewArg) {
          return buildRoute(CustomWebviewScreen(arg: args));
        }
        return errorScreen('No route defined for ${settings.name}');

      default:
        return errorScreen('No route defined for ${settings.name}');
    }
  }

  static CupertinoPageRoute errorScreen(String msg) {
    return CupertinoPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text(msg),
        ),
      ),
    );
  }
}
