import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class CartProductCard extends ConsumerStatefulWidget {
  const CartProductCard({
    super.key,
    this.cartItem,
    this.showIncrementBtn = false,
    this.showRemoveBtn = false,
    this.allowSlideToDelete = false,
    this.onRemoveTap,
  });

  final CartItem? cartItem;
  final bool showIncrementBtn;
  final bool showRemoveBtn;
  final bool allowSlideToDelete;
  final VoidCallback? onRemoveTap;

  @override
  ConsumerState<CartProductCard> createState() => _CartProductCardState();
}

class _CartProductCardState extends ConsumerState<CartProductCard> {
  final qtyController = TextEditingController();
  int? _qty;

  @override
  void dispose() {
    qtyController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _qty = widget.cartItem?.quantity;
      qtyController.text = widget.cartItem?.quantity.toString() ?? "";
      setState(() {});
    });
  }

  // void _showDeleteConfirmationModal() {
  //   final loadingProvider = StateProvider<bool>((ref) => false);
  //   ModalWrapper.bottomSheet(
  //     context: context,
  //     widget: Consumer(
  //       builder: (context, ref, child) {
  //         final isLoading = ref.watch(loadingProvider);
  //         return ConfirmModal(
  //           title: "Remove product",
  //           subtitle: "Are you sure you want to remove this product?",
  //           rightBtnText: "Remove",
  //           isLoading: isLoading,
  //           rightBtnTap: () async {
  //             ref.read(loadingProvider.notifier).state = true;

  //             try {
  //               await ref.read(cartVm.notifier).removeCartOrWishlist(
  //                     itemId: widget.cartItem?.id ?? "",
  //                     isWishList: false,
  //                   );
  //             } finally {
  //               if (context.mounted) {
  //                 ref.read(loadingProvider.notifier).state = false;
  //                 Navigator.pop(context);
  //               }
  //             }
  //           },
  //         );
  //       },
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    final cardContent = Row(
      crossAxisAlignment: widget.showRemoveBtn
          ? CrossAxisAlignment.center
          : CrossAxisAlignment.end,
      children: [
        SizedBox(
          width: Sizer.width(94),
          height: Sizer.height(90),
          child: MyCachedNetworkImage(
            imageUrl: widget.cartItem?.image ?? "",
          ),
        ),
        const XBox(16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                (widget.cartItem?.variationName ?? "").toUpperCase(),
                style: AppTypography.text10.copyWith(
                    fontWeight: FontWeight.w600, color: AppColors.yellow37),
              ),
              Text(
                widget.cartItem?.productName ?? "",
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                widget.cartItem?.cartonSize ?? "",
                style: AppTypography.text12.copyWith(
                  color: AppColors.black70,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const YBox(8),
              Text(
                '${AppUtils.nairaSymbol}${AppUtils.formatNumber(decimalPlaces: 0, number: widget.cartItem?.price ?? 0)}',
                style: AppTypography.text12.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              )
            ],
          ),
        ),
        const XBox(16),
        if (widget.showIncrementBtn)
          CartIncrement(
            controller: qtyController,
            value: _qty ?? 1,
            onCrement: () {
              printty("onCrement");
              final newQty = (_qty ?? 1) + 1;
              _qty = newQty;
              qtyController.text = newQty.toString();
              setState(() {});
              ref
                  .read(cartVm.notifier)
                  .updateCartOrWishlist(
                    itemId: widget.cartItem?.id ?? "",
                    quantity: newQty,
                  )
                  .whenComplete(() {
                if (ref.read(cartVm).apiResponse.success == false) {
                  setState(() {
                    _qty = (_qty ?? 1) - 1;
                    qtyController.text = (_qty ?? 1).toString();
                  });
                }
              });
            },
            onDecrement: () {
              printty("onDecrement");
              final newQty = (_qty ?? 1) - 1;
              _qty = newQty;
              qtyController.text = newQty.toString();
              setState(() {});
              ref
                  .read(cartVm.notifier)
                  .updateCartOrWishlist(
                    itemId: widget.cartItem?.id ?? "",
                    quantity: newQty,
                  )
                  .whenComplete(() {
                if (ref.read(cartVm).apiResponse.success == false) {
                  setState(() {
                    _qty = (_qty ?? 1) + 1;
                    qtyController.text = (_qty ?? 1).toString();
                  });
                }
              });
            },
          )
        else if (widget.showRemoveBtn)
          InkWell(
            onTap: widget.onRemoveTap,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(12),
                vertical: Sizer.height(8),
              ),
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.blackBD,
                ),
              ),
              child: Text(
                "Remove",
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
      ],
    );
    if (widget.allowSlideToDelete) {
      return Dismissible(
        key: Key(widget.cartItem?.id ?? DateTime.now().toString()),
        direction: DismissDirection.endToStart,
        confirmDismiss: (direction) async {
          // Show the confirmation modal and wait for result
          _showDeleteConfirmationModal();
          // Always cancel the automatic dismissal, we'll handle removal via modal
          return false;
        },
        background: Container(
          alignment: Alignment.centerRight,
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
          color: Colors.red,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'Delete',
                style: AppTypography.text14.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: Sizer.width(8)),
              Icon(
                Icons.delete,
                color: Colors.white,
                size: Sizer.radius(24),
              ),
            ],
          ),
        ),
        child: Container(
          color: Colors.white,
          child: cardContent,
        ),
      );
    } else {
      return Container(
        color: Colors.white,
        child: cardContent,
      );
    }
  }
}
